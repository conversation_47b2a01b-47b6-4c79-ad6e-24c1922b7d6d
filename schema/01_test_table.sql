-- Test table to verify schema initialization
CREATE TABLE IF NOT EXISTS test_table (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

-- Insert some test data
INSERT INTO test_table (name, description) VALUES 
    ('Test Record 1', 'This is a test record to verify schema initialization'),
    ('Test Record 2', 'Another test record for verification'),
    ('Mining Pool Test', 'Test record for mining pool database setup');

-- Create an index for testing
CREATE INDEX IF NOT EXISTS idx_test_table_name ON test_table(name);
