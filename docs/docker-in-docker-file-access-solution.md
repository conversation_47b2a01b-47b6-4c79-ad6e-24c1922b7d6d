# Docker-in-Docker 文件访问解决方案

## 概述

本文档记录了在 Woodpecker CI 环境中解决 Docker-in-Docker (DinD) 文件访问问题的完整解决方案。

## 问题背景

在 Woodpecker CI 中使用 Docker-in-Docker 时，遇到了以下问题：
- CI 容器内可以看到代码文件
- 但 DinD 容器无法访问这些文件
- 传统的卷挂载方式失败

## 根本原因

Docker-in-Docker 环境中存在多层文件系统隔离：

```
Woodpecker 宿主机
├── /woodpecker/src/github.com/dogehash/mining-pool/  ← 原始代码
│
└── CI 容器 (cruizba/ubuntu-dind:jammy-latest)
    ├── 挂载: 宿主机代码 → /workspace
    ├── 可以看到: /workspace/scripts/init-dogecoin-wallet.sh ✅
    │
    └── DinD 容器
        ├── 尝试挂载: /workspace/scripts → 容器内
        ├── 但外层 Docker 守护进程找不到 /workspace 路径 ❌
        └── 结果: 文件访问失败
```

## 解决方案：优化的共享目录策略

### 核心思路

通过直接映射工作目录到宿主机共享路径，避免文件复制，实现高效的文件访问：

```
步骤1: 检查环境变量和工作目录
步骤2: 验证 CI 容器内的代码
步骤3: 验证宿主机可访问共享目录
步骤4: DinD 容器访问测试
```

### 优化后的实现架构

```
Woodpecker 宿主机
├── /tmp/shared-workspace ← 宿主机共享路径
│
└── CI 容器
    ├── 直接挂载: /tmp/shared-workspace → /workspace
    ├── 工作目录: /workspace (包含所有代码)
    │
    └── DinD 容器
        ├── 挂载: $SHARED_WORKSPACE/scripts → /scripts
        └── 直接访问文件 ✅
```

## 技术实现

### 1. 优化后的 Woodpecker CI 配置

```yaml
steps:
  - name: simple-test
    image: cruizba/ubuntu-dind:jammy-latest
    privileged: true
    environment:
      SHARED_WORKSPACE: /tmp/shared-workspace
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化：直接映射工作目录到宿主机共享路径
      - /tmp/shared-workspace:/workspace
    working_dir: /workspace
```

### 2. 优化后的四步骤实现流程

#### 步骤1: 检查环境变量和工作目录
```bash
echo "=== Step 1 Check environment and variables ==="
echo "Current working directory $(pwd)"
echo "SHARED_WORKSPACE variable $SHARED_WORKSPACE"
```

#### 步骤2: 验证 CI 容器内的代码
```bash
echo "=== Step 2 Check code in CI container ==="
echo "CI container contents:"
ls -la
echo "Check scripts directory:"
ls -la scripts/ || echo "No scripts directory"
```

#### 步骤3: 验证宿主机可以访问共享目录
```bash
echo "=== Step 3 Verify host can access shared directory ==="
docker run --rm -v "$SHARED_WORKSPACE:/workspace" alpine ls -la /workspace/scripts/
```

#### 步骤4: Docker-in-Docker 访问测试
```bash
echo "=== Step 4 Docker-in-Docker access test ==="
docker run --rm -v "$SHARED_WORKSPACE/scripts:/scripts" alpine ls -la /scripts/
docker run --rm -v "$SHARED_WORKSPACE/scripts:/scripts" alpine head -3 /scripts/init-dogecoin-wallet.sh
```

## 测试结果

✅ **成功输出**:
```
File accessible!
#!/bin/bash
```

这证明了：
1. 文件成功从 CI 容器复制到共享目录
2. 宿主机可以访问共享目录
3. DinD 容器可以挂载并读取脚本文件
4. 文件内容完整且可访问

## 关键技术点

### 1. 全局变量管理
- **环境变量**: `SHARED_WORKSPACE: /tmp/shared-workspace`
- **统一路径管理**: 所有地方使用 `$SHARED_WORKSPACE` 变量
- **易于维护**: 修改路径只需更改一个地方

### 2. 直接目录映射策略
- **CI 容器工作目录**: `/workspace`
- **宿主机共享路径**: `/tmp/shared-workspace`
- **直接映射**: `/tmp/shared-workspace:/workspace`
- **无需文件复制**: 实时同步，性能更优

### 3. 多层验证
- 每个步骤都有独立的验证
- 清晰的错误提示
- 完整的调试信息

## 适用场景

这个解决方案适用于：
- Woodpecker CI 环境
- Docker-in-Docker 场景
- 需要在 DinD 容器中访问代码文件
- 传统卷挂载失败的情况

## 优化优势

1. **性能提升**: 无需文件复制，节省时间和 I/O
2. **实时同步**: 代码变更立即反映到共享目录
3. **资源节省**: 不占用额外磁盘空间
4. **简化流程**: 减少了复制和验证步骤
5. **易于维护**: 使用全局变量，便于路径管理

## 注意事项

1. **权限管理**: 确保共享目录权限正确
2. **路径一致性**: 确保 SHARED_WORKSPACE 变量在所有地方正确使用
3. **清理机制**: 构建完成后清理临时文件

## 扩展应用

这个策略可以扩展到：
- 其他 CI/CD 系统
- 复杂的多容器应用部署
- 需要文件共享的微服务架构

## 总结

通过共享目录策略，我们成功解决了 Docker-in-Docker 环境中的文件访问问题。这个解决方案：
- ✅ 绕过了 DinD 路径映射问题
- ✅ 提供了可靠的文件访问机制
- ✅ 具有良好的调试和验证能力
- ✅ 可以应用到类似的场景中

这是一个经过实战验证的、可靠的 Docker-in-Docker 文件访问解决方案。
